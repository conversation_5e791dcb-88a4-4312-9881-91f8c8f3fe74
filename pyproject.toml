[project]
name = "chatterbox-tts"
version = "0.0.1"
description = "Chatterbox TTS is a text-to-speech (TTS) library for Python."
readme = "README.md"
requires-python = ">=3.9"
authors = [
    { name = "Usama Arshad", email = "<EMAIL>" },
]
license = { file = "LICENSE" }
dependencies = [
    "numpy>=1.26.0",
    "librosa==0.11.0",
    "s3tokenizer",
    "torch==2.6.0",
    "torchaudio==2.6.0",
    "transformers==4.46.3",
    "diffusers==0.29.0",
    "resemble-perth==1.0.1",
    "conformer==0.3.2",
    "safetensors==0.5.3",
    "gradio>=4.0.0"
]

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]
include = ["chatterbox*"]