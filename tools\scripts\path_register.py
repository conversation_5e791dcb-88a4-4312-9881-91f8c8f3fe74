import os
import sys
import platform
from pathlib import Path

def add_to_windows_path(target_path):
    import winreg
    with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Environment", 0, winreg.KEY_ALL_ACCESS) as key:
        try:
            current_value, _ = winreg.QueryValueEx(key, "Path")
        except FileNotFoundError:
            current_value = ""
        paths = current_value.split(";")
        if target_path in paths:
            return
        paths.append(target_path)
        updated = ";".join(p for p in paths if p)
        winreg.SetValueEx(key, "Path", 0, winreg.REG_EXPAND_SZ, updated)

def add_to_unix_path_instruction(target_path):
    shell_rc = Path.home() / ".bashrc"
    export_line = f'export PATH="{target_path}:$PATH"'
    with open(shell_rc, "r") as file:
        lines = file.readlines()
    if any(export_line in line for line in lines):
        return
    with open(shell_rc, "a") as file:
        file.write(f"\n{export_line}\n")

def main():
    bin_path = Path(__file__).resolve().parent.parent / "bin"
    if platform.system() == "Windows":
        add_to_windows_path(str(bin_path))
    else:
        add_to_unix_path_instruction(str(bin_path))
        print("Add the above line to your shell rc file and restart your terminal.")

if __name__ == "__main__":
    main()
