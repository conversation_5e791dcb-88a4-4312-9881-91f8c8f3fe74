import os
import subprocess
import venv
from pathlib import Path

ROOT_DIR = Path(__file__).resolve().parent.parent
VENV_PATH = ROOT_DIR / ".venv"
IS_WINDOWS = os.name == "nt"

def create_venv():
    if not VENV_PATH.exists():
        venv.EnvBuilder(with_pip=True).create(VENV_PATH)

def get_python_executable():
    return VENV_PATH / ("Scripts" if IS_WINDOWS else "bin") / "python"

def install_deps(python):
    subprocess.run([python, "-m", "pip", "install", "--upgrade", "pip"], check=True)
    subprocess.run([python, "-m", "pip", "install", "-e", str(ROOT_DIR)], check=True)

def main():
    create_venv()
    python = get_python_executable()
    install_deps(python)
    print("\nEnvironment is ready.")
    if IS_WINDOWS:
        print(f"To activate:\n  {VENV_PATH}\\Scripts\\activate.bat")
    else:
        print(f"To activate:\n  source {VENV_PATH}/bin/activate")

if __name__ == "__main__":
    main()
